// image-processors.js
// Image processing functions

import { joinImages } from 'join-images';
import crypto from 'crypto';
import https from 'https';
import { Buffer } from 'buffer';
import { getStorageInstance } from '../config/firebase-config.js';
import { DEFAULT_BUCKET_NAME } from '../config/constants.js';

/**
 * Process Instagram image posts
 * @param {Object} data - The Instagram data
 * @returns {string} - The base64-encoded merged image
 */
async function processInstagramImagePosts(data) {
  console.log(`🖼️ IMAGE_PROCESSING_START: Beginning image processing for influencer data`);
  const imageUrls = [];

  // Check if Instagram data exists
  if (!data.instagram) {
    console.log("❌ IMAGE_PROCESSING_ERROR: No Instagram data found.");
    console.log(`📊 IMAGE_PROCESSING_DATA_STRUCTURE: Input data keys:`, Object.keys(data || {}));
    return '';
  }

  console.log(`📊 IMAGE_PROCESSING_INSTAGRAM_STRUCTURE: Instagram data keys:`, Object.keys(data.instagram));

  // Append the HD profile picture as the first image, if available
  if (data.instagram.profile_picture_hd) {
    // Validate the profile picture URL
    if (typeof data.instagram.profile_picture_hd === 'string' && data.instagram.profile_picture_hd.trim() !== '') {
      imageUrls.push(data.instagram.profile_picture_hd);
      console.log(`✅ IMAGE_PROCESSING_PROFILE_PIC: Profile Picture Added: ${data.instagram.profile_picture_hd}`);
    } else {
      console.log('❌ IMAGE_PROCESSING_PROFILE_PIC: Profile picture URL is invalid or empty');
      console.log(`❌ IMAGE_PROCESSING_PROFILE_PIC_TYPE: Type: ${typeof data.instagram.profile_picture_hd}, Value: ${JSON.stringify(data.instagram.profile_picture_hd)}`);
    }
  } else {
    console.log('❌ IMAGE_PROCESSING_PROFILE_PIC: No profile_picture_hd found in the data');
  }

  // Check if post_data exists and is an array
  if (Array.isArray(data.instagram.post_data) && data.instagram.post_data.length > 0) {
    console.log(`📊 IMAGE_PROCESSING_POST_DATA: Total posts in data.instagram.post_data: ${data.instagram.post_data.length}`);
    console.log('📊 IMAGE_PROCESSING_FIRST_POST_STRUCTURE: Structure of first post:', JSON.stringify(data.instagram.post_data[0], null, 2));

    // Process each post: get the first image from each post's media array
    // DEBUG: Limit to the last 6 image posts as requested
    let processedCount = 0;
    data.instagram.post_data.forEach((post, index) => {
      console.log(`🔍 IMAGE_PROCESSING_POST_EXAMINE: Examining post #${index + 1}`);

      // DEBUG: Check if post has media array
      if (!Array.isArray(post.media)) {
        console.log(`❌ IMAGE_PROCESSING_POST_NO_MEDIA: Post #${index + 1} has no media array or it's not an array`);
        console.log(`❌ IMAGE_PROCESSING_POST_STRUCTURE: Post structure:`, JSON.stringify(post, null, 2));
        return; // Skip this post
      }

      console.log(`📊 IMAGE_PROCESSING_POST_MEDIA_COUNT: Post #${index + 1} has ${post.media.length} media items`);

      // DEBUG: Log the types of media in this post
      const mediaTypes = post.media.map(item => item.type);
      console.log(`📊 IMAGE_PROCESSING_POST_MEDIA_TYPES: Media types in post #${index + 1}:`, mediaTypes);

      // Log each media item for detailed debugging
      post.media.forEach((mediaItem, mediaIndex) => {
        console.log(`📊 IMAGE_PROCESSING_MEDIA_ITEM: Post #${index + 1}, Media #${mediaIndex + 1}:`, {
          type: mediaItem.type,
          hasUrl: !!mediaItem.url,
          url: mediaItem.url ? `${mediaItem.url.substring(0, 50)}...` : 'NO_URL'
        });
      });

      const firstImageItem = post.media.find(mediaItem => mediaItem.type === 'image' && mediaItem.url);
      if (firstImageItem) {
        imageUrls.push(firstImageItem.url);
        processedCount++;
        console.log(`✅ IMAGE_PROCESSING_IMAGE_FOUND: Image Found (${processedCount}): ${firstImageItem.url}`);
      } else {
        console.log(`❌ IMAGE_PROCESSING_NO_IMAGE: No valid image found in post #${index + 1}`);
      }

      // DEBUG: Stop after processing 6 images (profile + 6 posts = 7 total)
      if (processedCount >= 6) {
        console.log(`🛑 IMAGE_PROCESSING_LIMIT_REACHED: Reached limit of 6 post images`);
        return;
      }
    });
  } else {
    console.log("❌ IMAGE_PROCESSING_NO_POST_DATA: Instagram post_data is not available or is not an array. Will use only profile picture if available.");
    console.log(`📊 IMAGE_PROCESSING_POST_DATA_TYPE: post_data type:`, typeof data.instagram.post_data);
    console.log(`📊 IMAGE_PROCESSING_POST_DATA_VALUE: post_data value:`, data.instagram.post_data);
  }

  // DEBUG: Log final image count
  console.log(`📊 IMAGE_PROCESSING_FINAL_COUNT: Total images collected: ${imageUrls.length} (including profile pic)`);
  console.log(`📊 IMAGE_PROCESSING_URL_LIST: Image URLs:`, imageUrls.map(url => `${url.substring(0, 50)}...`));

  // If no images are found, return an empty string.
  if (imageUrls.length === 0) {
    console.log("❌ IMAGE_PROCESSING_NO_IMAGES: No images found for analysis. Returning an empty string.");
    return '';
  }

  // Download all images concurrently with error handling
  console.log(`🔄 IMAGE_PROCESSING_DOWNLOAD_START: Attempting to download ${imageUrls.length} images`);

  try {
    // Use Promise.allSettled to handle individual download failures
    const downloadResults = await Promise.allSettled(imageUrls.map((url, index) => {
      console.log(`🔄 IMAGE_PROCESSING_DOWNLOAD_ATTEMPT: Downloading image ${index + 1}/${imageUrls.length}: ${url.substring(0, 50)}...`);
      return downloadImage(url);
    }));

    // Log download results in detail
    downloadResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(`✅ IMAGE_PROCESSING_DOWNLOAD_SUCCESS: Image ${index + 1} downloaded successfully (${result.value.length} bytes)`);
      } else {
        console.log(`❌ IMAGE_PROCESSING_DOWNLOAD_FAILED: Image ${index + 1} failed: ${result.reason.message}`);
      }
    });

    // Filter out failed downloads
    const successfulDownloads = downloadResults
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);

    console.log(`📊 IMAGE_PROCESSING_DOWNLOAD_SUMMARY: Successfully downloaded ${successfulDownloads.length} of ${imageUrls.length} images`);

    // If no images were successfully downloaded, return empty string
    if (successfulDownloads.length === 0) {
      console.log("❌ IMAGE_PROCESSING_NO_SUCCESSFUL_DOWNLOADS: No images could be downloaded successfully. Returning an empty string.");
      return '';
    }

    // Merge the images vertically using join-images
    console.log(`🔄 IMAGE_PROCESSING_MERGE_START: Merging ${successfulDownloads.length} images vertically`);
    const mergedImage = await joinImages(successfulDownloads, { direction: 'vertical' });
    console.log(`✅ IMAGE_PROCESSING_MERGE_SUCCESS: Images merged successfully`);

    // Convert the merged image to JPEG format and get the buffer
    console.log(`🔄 IMAGE_PROCESSING_CONVERT_START: Converting merged image to JPEG format`);
    const mergedBuffer = await mergedImage.toFormat('jpeg').toBuffer();
    const base64Image = mergedBuffer.toString('base64');
    console.log(`✅ IMAGE_PROCESSING_CONVERT_SUCCESS: Final image converted to base64 (length: ${base64Image.length})`);

    return base64Image;
  } catch (error) {
    console.error(`❌ IMAGE_PROCESSING_ERROR: Error processing images: ${error.message}`);
    console.error(`❌ IMAGE_PROCESSING_ERROR_STACK:`, error.stack);
    return '';
  }
}

/**
 * Download an image from a URL
 * @param {string} url - The image URL
 * @returns {Buffer} - The image buffer
 */
function downloadImage(url) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 DOWNLOAD_IMAGE_START: Starting download for URL: ${url.substring(0, 100)}...`);

    // Validate URL
    if (!url || typeof url !== 'string' || url.trim() === '') {
      console.log(`❌ DOWNLOAD_IMAGE_INVALID_URL: Invalid image URL - Type: ${typeof url}, Value: ${JSON.stringify(url)}`);
      return reject(new Error('Invalid image URL'));
    }

    // Set a timeout for the request (10 seconds)
    const timeout = 10000;
    const timeoutId = setTimeout(() => {
      console.log(`❌ DOWNLOAD_IMAGE_TIMEOUT: Request timed out after ${timeout}ms for URL: ${url.substring(0, 50)}...`);
      req.destroy();
      reject(new Error(`Request timed out after ${timeout}ms`));
    }, timeout);

    const req = https.get(url, (res) => {
      console.log(`📊 DOWNLOAD_IMAGE_RESPONSE: Received response - Status: ${res.statusCode}, Content-Type: ${res.headers['content-type']}`);

      // Clear the timeout
      clearTimeout(timeoutId);

      // Check for redirect
      if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
        console.log(`🔄 DOWNLOAD_IMAGE_REDIRECT: Following redirect to: ${res.headers.location}`);
        // Recursively follow redirects
        return downloadImage(res.headers.location)
          .then(resolve)
          .catch(reject);
      }

      // Check for successful status code
      if (res.statusCode !== 200) {
        console.log(`❌ DOWNLOAD_IMAGE_HTTP_ERROR: HTTP Error ${res.statusCode} for URL: ${url.substring(0, 50)}...`);
        return reject(new Error(`HTTP Error: ${res.statusCode}`));
      }

      // Check content type
      const contentType = res.headers['content-type'] || '';
      if (!contentType.startsWith('image/')) {
        console.log(`❌ DOWNLOAD_IMAGE_INVALID_CONTENT_TYPE: Invalid content type '${contentType}' for URL: ${url.substring(0, 50)}...`);
        return reject(new Error(`Invalid content type: ${contentType}`));
      }

      console.log(`✅ DOWNLOAD_IMAGE_VALID_RESPONSE: Valid image response received for URL: ${url.substring(0, 50)}...`);

      const chunks = [];
      res.on('data', (chunk) => {
        chunks.push(chunk);
        console.log(`📊 DOWNLOAD_IMAGE_DATA_CHUNK: Received chunk of ${chunk.length} bytes (total chunks: ${chunks.length})`);
      });

      res.on('end', () => {
        try {
          const buffer = Buffer.concat(chunks);
          console.log(`📊 DOWNLOAD_IMAGE_COMPLETE: Download complete - Total size: ${buffer.length} bytes`);

          // Ensure we have actual image data
          if (buffer.length === 0) {
            console.log(`❌ DOWNLOAD_IMAGE_EMPTY: Empty image data received for URL: ${url.substring(0, 50)}...`);
            return reject(new Error('Empty image data received'));
          }

          console.log(`✅ DOWNLOAD_IMAGE_SUCCESS: Successfully downloaded image (${buffer.length} bytes) from: ${url.substring(0, 50)}...`);
          resolve(buffer);
        } catch (error) {
          console.log(`❌ DOWNLOAD_IMAGE_BUFFER_ERROR: Error creating buffer: ${error.message}`);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ DOWNLOAD_IMAGE_REQUEST_ERROR: Request error for URL ${url.substring(0, 50)}...: ${error.message}`);
      clearTimeout(timeoutId);
      reject(error);
    });

    // Set a timeout for the entire request
    req.setTimeout(timeout, () => {
      console.log(`❌ DOWNLOAD_IMAGE_REQUEST_TIMEOUT: Request timeout for URL: ${url.substring(0, 50)}...`);
      req.destroy();
      clearTimeout(timeoutId);
      reject(new Error('Request timed out'));
    });
  });
}

/**
 * Cache an image in Cloud Storage
 * @param {string} imageUrl - The image URL
 * @param {string} influencerId - The influencer ID (optional)
 * @param {boolean} isProfilePicture - Whether this is a profile picture (optional)
 * @param {number} retryCount - Current retry attempt (internal use)
 * @returns {string} - The cached image URL
 */
async function cacheImage(imageUrl, influencerId = null, isProfilePicture = false, retryCount = 0) {
  if (!imageUrl) return "";

  const maxRetries = 3;
  const bucketName = DEFAULT_BUCKET_NAME;

  try {
    // Get storage instance
    const storage = getStorageInstance();

    // Generate a unique filename based on the URL
    const urlHash = crypto.createHash('md5').update(imageUrl).digest('hex');
    const filename = `${urlHash}.jpg`;

    // Determine the appropriate cache path based on parameters
    let cachePath;
    if (influencerId) {
      if (isProfilePicture) {
        cachePath = `influencers/${influencerId}/profile/${filename}`;
      } else {
        cachePath = `influencers/${influencerId}/images/${filename}`;
      }
    } else {
      cachePath = `image-cache/${filename}`;
    }

    // Check if the image is already cached
    try {
      const [metadata] = await storage.bucket(bucketName).file(cachePath).getMetadata();
      console.log(`Image already cached: ${imageUrl}`);

      // Check if the cached image has a signed URL
      if (metadata.metadata && metadata.metadata.signedUrl) {
        console.log(`Using existing signed URL`);
        return metadata.metadata.signedUrl;
      }

      return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
    } catch (error) {
      // Image not cached, proceed with download
      console.log(`Image not cached, downloading: ${imageUrl}`);
    }

    // Download the image
    let contentType = 'image/jpeg';
    const buffer = await new Promise((resolve, reject) => {
      https.get(imageUrl, (res) => {
        contentType = res.headers['content-type'] || 'image/jpeg';
        const chunks = [];
        res.on('data', (chunk) => chunks.push(chunk));
        res.on('end', () => resolve(Buffer.concat(chunks)));
      }).on('error', reject);
    });

    // Upload to Cloud Storage with metadata
    await storage.bucket(bucketName).file(cachePath).save(buffer, {
      metadata: {
        contentType: contentType,
        cacheControl: 'public, max-age=31536000', // 1 year in seconds
        metadata: {
          cachedAt: new Date().toISOString()
        }
      }
    });

    // Set CORS configuration for the file
    await storage.bucket(bucketName).file(cachePath).setMetadata({
      metadata: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Max-Age': '3600'
      }
    });

    // Make the file publicly accessible
    try {
      await storage.bucket(bucketName).file(cachePath).makePublic();
      console.log(`Image cached successfully: ${imageUrl}`);
      return `https://storage.googleapis.com/${bucketName}/${cachePath}`;
    } catch (permissionError) {
      console.warn(`Warning: Unable to make file public due to permissions. Using signed URL instead: ${permissionError.message}`);

      // Generate a signed URL as fallback (valid for 7 days - max allowed)
      const [signedUrl] = await storage.bucket(bucketName).file(cachePath).getSignedUrl({
        version: 'v4',
        action: 'read',
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days (max allowed)
      });

      // Store the signed URL in the file's metadata
      await storage.bucket(bucketName).file(cachePath).setMetadata({
        metadata: {
          signedUrl: signedUrl
        }
      });

      console.log(`Generated signed URL for image: ${signedUrl}`);
      return signedUrl;
    }
  } catch (error) {
    console.error(`Error caching image (attempt ${retryCount + 1}/${maxRetries}): ${error.message}`);

    // Retry logic
    if (retryCount < maxRetries - 1) {
      console.log(`Retrying image caching in 1 second...`);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
      return cacheImage(imageUrl, influencerId, isProfilePicture, retryCount + 1);
    }

    // After all retries failed, return the original URL
    console.error(`Failed to cache image after ${maxRetries} attempts: ${imageUrl}`);
    return imageUrl;
  }
}

export {
  processInstagramImagePosts,
  downloadImage,
  cacheImage
};
