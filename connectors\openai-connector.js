// openai-connector.js
// OpenAI API connector

import OpenAI from 'openai';
import { OPENAI_API_KEY, PRICING_RATES } from '../config/constants.js';
import { extractJSON } from '../utils/string-utils.js';

/**
 * OpenAI API connector
 * Supports both Assistants API and Responses API
 */
class OpenAIConnector {
  /**
   * Create a new OpenAI connector
   * @param {string} apiKey - The OpenAI API key
   */
  constructor(apiKey = OPENAI_API_KEY) {
    this.client = new OpenAI({ apiKey });
    this.pricingRates = PRICING_RATES;
  }

  /**
   * Process a request using the Responses API with web search
   * @param {string} model - The model to use (e.g., "gpt-4.1")
   * @param {string} instructions - The system instructions
   * @param {string} prompt - The user prompt
   * @param {boolean} useWebSearch - Whether to use web search
   * @param {string} format - The output format (e.g., "json_object")
   * @returns {Object} - The processed result
   */
  async processWithResponses(model, instructions, prompt, useWebSearch = true, format = "json_object") {
    try {
      console.log(`🤖 OPENAI_RESPONSES_START: Processing with Responses API using model ${model}...`);
      console.log(`📊 OPENAI_RESPONSES_PARAMS: useWebSearch=${useWebSearch}, format=${format}`);
      console.log(`📝 OPENAI_RESPONSES_INSTRUCTIONS_LENGTH: Instructions length: ${instructions ? instructions.length : 'NULL'} characters`);
      console.log(`📝 OPENAI_RESPONSES_PROMPT_LENGTH: Prompt length: ${prompt ? prompt.length : 'NULL'} characters`);

      // Configure the request
      const requestOptions = {
        model: model,
        instructions: instructions,
        text: {
          format: {
            type: format
          }
        },
        input: prompt
      };

      // Add web search tool if requested
      if (useWebSearch) {
        requestOptions.tools = [{
          type: "web_search_preview",
          search_context_size: "high" // Use high context size for comprehensive research
        }];
        console.log(`🔍 OPENAI_RESPONSES_WEB_SEARCH: Web search enabled with high context size`);
      } else {
        console.log(`🚫 OPENAI_RESPONSES_NO_WEB_SEARCH: Web search disabled`);
      }

      console.log(`📊 OPENAI_RESPONSES_REQUEST_OPTIONS: Request options keys: ${Object.keys(requestOptions)}`);
      console.log("🔄 OPENAI_RESPONSES_SENDING: Making request to Responses API...");

      // Make the request
      const response = await this.client.responses.create(requestOptions);

      console.log("✅ OPENAI_RESPONSES_RECEIVED: Response received from Responses API");
      console.log(`📊 OPENAI_RESPONSES_STRUCTURE: Response keys: ${Object.keys(response)}`);
      console.log(`📊 OPENAI_RESPONSES_OUTPUT_TEXT: Has output_text: ${!!response.output_text}`);
      console.log(`📊 OPENAI_RESPONSES_OUTPUT_ARRAY: Has output array: ${!!response.output}, Length: ${response.output ? response.output.length : 'NULL'}`);

      // Extract the JSON response
      let parsedResponse;

      // Check if the response has output_text
      if (response.output_text) {
        console.log(`📝 OPENAI_RESPONSES_OUTPUT_TEXT_LENGTH: output_text length: ${response.output_text.length} characters`);
        console.log(`📝 OPENAI_RESPONSES_OUTPUT_TEXT_PREVIEW: output_text preview (first 200 chars): ${response.output_text.substring(0, 200)}...`);
        try {
          console.log(`🔄 OPENAI_RESPONSES_JSON_PARSE: Attempting to parse output_text as JSON...`);
          parsedResponse = JSON.parse(response.output_text);
          console.log(`✅ OPENAI_RESPONSES_JSON_PARSE_SUCCESS: Successfully parsed output_text as JSON`);
        } catch (error) {
          console.error('❌ OPENAI_RESPONSES_JSON_PARSE_ERROR: Error parsing JSON from response.output_text:', error);
          console.log(`🔄 OPENAI_RESPONSES_EXTRACT_JSON: Trying extractJSON utility function...`);
          // Try to extract JSON from the text using our utility function
          parsedResponse = extractJSON(response.output_text);
          if (parsedResponse) {
            console.log(`✅ OPENAI_RESPONSES_EXTRACT_JSON_SUCCESS: Successfully extracted JSON using utility function`);
          } else {
            console.log(`❌ OPENAI_RESPONSES_EXTRACT_JSON_FAILED: extractJSON utility function also failed`);
          }
        }
      } else if (response.output && response.output.length > 0) {
        console.log(`📊 OPENAI_RESPONSES_OUTPUT_ARRAY_PROCESSING: Processing output array with ${response.output.length} items`);
        // Find the message in the output
        const messageOutput = response.output.find(item => item.type === "message");
        console.log(`📊 OPENAI_RESPONSES_MESSAGE_OUTPUT: Found message output: ${!!messageOutput}`);
        if (messageOutput && messageOutput.content && messageOutput.content.length > 0) {
          console.log(`📊 OPENAI_RESPONSES_MESSAGE_CONTENT: Message content length: ${messageOutput.content.length}`);
          const textContent = messageOutput.content.find(content => content.type === "output_text");
          console.log(`📊 OPENAI_RESPONSES_TEXT_CONTENT: Found text content: ${!!textContent}`);
          if (textContent && textContent.text) {
            console.log(`📝 OPENAI_RESPONSES_TEXT_CONTENT_LENGTH: Text content length: ${textContent.text.length} characters`);
            console.log(`📝 OPENAI_RESPONSES_TEXT_CONTENT_PREVIEW: Text content preview (first 200 chars): ${textContent.text.substring(0, 200)}...`);
            console.log(`🔄 OPENAI_RESPONSES_EXTRACT_JSON_FROM_TEXT: Extracting JSON from text content...`);
            parsedResponse = extractJSON(textContent.text);
            if (parsedResponse) {
              console.log(`✅ OPENAI_RESPONSES_EXTRACT_JSON_FROM_TEXT_SUCCESS: Successfully extracted JSON from text content`);
            } else {
              console.log(`❌ OPENAI_RESPONSES_EXTRACT_JSON_FROM_TEXT_FAILED: Failed to extract JSON from text content`);
            }
          }
        }
      }

      console.log(`📊 OPENAI_RESPONSES_PARSED_RESULT: Parsed response type: ${typeof parsedResponse}`);
      console.log(`📊 OPENAI_RESPONSES_PARSED_RESULT_KEYS: Parsed response keys: ${parsedResponse ? Object.keys(parsedResponse) : 'NULL'}`);

      if (!parsedResponse) {
        console.error(`❌ OPENAI_RESPONSES_NO_PARSED_RESULT: Failed to extract JSON from the response`);
        throw new Error('Failed to extract JSON from the response');
      }

      console.log(`✅ OPENAI_RESPONSES_SUCCESS: Successfully processed response with Responses API`);
      return parsedResponse;
    } catch (error) {
      console.error('❌ OPENAI_RESPONSES_ERROR: Error processing with Responses API:', error);
      console.error('❌ OPENAI_RESPONSES_ERROR_STACK:', error.stack);
      throw error;
    }
  }

  /**
   * Process an agent with a prompt
   * @param {string} agentID - The agent ID
   * @param {string} prompt - The prompt
   * @returns {Object} - The processed result
   */
  async processAgent(agentID, prompt) {
    console.log(`🤖 OPENAI_AGENT_START: Processing agent ${agentID.substring(0, 20)}...`);
    console.log(`📝 OPENAI_AGENT_PROMPT_LENGTH: Prompt length: ${prompt ? prompt.length : 'NULL'} characters`);

    let attempt = 0;

    while (true) {
      attempt++;
      console.log(`🔄 OPENAI_AGENT_ATTEMPT_START: Starting attempt ${attempt}...`);

      try {
        // Create a new thread and send the prompt as a user message
        console.log(`🔄 OPENAI_AGENT_THREAD_CREATE: Creating new thread...`);
        const thread = await this.client.beta.threads.create();
        console.log(`✅ OPENAI_AGENT_THREAD_CREATED: Thread created with ID: ${thread.id}`);

        console.log(`🔄 OPENAI_AGENT_MESSAGE_CREATE: Creating user message in thread...`);
        await this.client.beta.threads.messages.create(thread.id, {
          role: "user",
          content: prompt,
        });
        console.log(`✅ OPENAI_AGENT_MESSAGE_CREATED: User message created successfully`);

        // Start the run with the specified assistant
        console.log(`🔄 OPENAI_AGENT_RUN_CREATE: Starting run with assistant ${agentID.substring(0, 20)}...`);
        const run = await this.client.beta.threads.runs.create(thread.id, {
          assistant_id: agentID,
        });
        console.log(`✅ OPENAI_AGENT_RUN_CREATED: Run created with ID: ${run.id}`);

        let cancelled = false;

        // Create a timeout promise that cancels the run after 120 seconds
        const timeoutPromise = new Promise((resolve) => {
          setTimeout(() => {
            console.log(`⏰ OPENAI_AGENT_TIMEOUT: Attempt ${attempt} timed out after 120 seconds, cancelling run...`);
            // Cancel the run and mark as cancelled
            this.client.beta.threads.runs.cancel(thread.id, run.id).catch(() => {});
            cancelled = true;
            resolve("timeout");
          }, 120000);
        });

        // Poll for the run's completion status every 3 seconds
        const pollPromise = (async () => {
          console.log(`🔄 OPENAI_AGENT_POLLING_START: Starting to poll for run completion...`);
          let runStatus = await this.client.beta.threads.runs.retrieve(thread.id, run.id);
          console.log(`📊 OPENAI_AGENT_INITIAL_STATUS: Initial run status: ${runStatus.status}`);

          while (runStatus.status !== "completed" && !cancelled) {
            await new Promise((resolve) => setTimeout(resolve, 3000));
            runStatus = await this.client.beta.threads.runs.retrieve(thread.id, run.id);
            console.log(`📊 OPENAI_AGENT_STATUS_UPDATE: Run status: ${runStatus.status}`);
          }
          console.log(`✅ OPENAI_AGENT_POLLING_COMPLETE: Polling completed with status: ${runStatus.status}`);
          return "completed";
        })();

        // Race between the polling and the timeout
        console.log(`🏁 OPENAI_AGENT_RACE_START: Racing between polling and timeout...`);
        const outcome = await Promise.race([timeoutPromise, pollPromise]);
        console.log(`🏁 OPENAI_AGENT_RACE_RESULT: Race outcome: ${outcome}`);

        if (outcome === "completed") {
          console.log(`✅ OPENAI_AGENT_COMPLETED: Run completed successfully, retrieving messages...`);
          // Retrieve messages from the thread
          const messagesResponse = await this.client.beta.threads.messages.list(thread.id);
          console.log(`📊 OPENAI_AGENT_MESSAGES_COUNT: Retrieved ${messagesResponse.data.length} total messages`);

          const assistantMessages = messagesResponse.data.filter(msg => msg.role === "assistant");
          console.log(`📊 OPENAI_AGENT_ASSISTANT_MESSAGES_COUNT: Found ${assistantMessages.length} assistant messages`);

          if (assistantMessages.length === 0) {
            console.error(`❌ OPENAI_AGENT_NO_ASSISTANT_RESPONSE: No assistant response found`);
            throw new Error("No assistant response found");
          }

          // Assume the last assistant message is the desired reply
          const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
          const assistantReply = lastAssistantMessage.content[0].text.value;
          console.log(`📝 OPENAI_AGENT_REPLY_LENGTH: Assistant reply length: ${assistantReply ? assistantReply.length : 'NULL'} characters`);
          console.log(`📝 OPENAI_AGENT_REPLY_PREVIEW: Assistant reply preview (first 200 chars): ${assistantReply ? assistantReply.substring(0, 200) : 'NULL'}...`);

          // Attempt to parse the assistant's reply as JSON
          let parsedReply;
          try {
            console.log(`🔄 OPENAI_AGENT_JSON_PARSE: Attempting to parse assistant reply as JSON...`);
            parsedReply = JSON.parse(assistantReply);
            console.log(`✅ OPENAI_AGENT_JSON_PARSE_SUCCESS: Successfully parsed assistant reply as JSON`);
            console.log(`📊 OPENAI_AGENT_PARSED_KEYS: Parsed reply keys: ${Object.keys(parsedReply)}`);
          } catch (error) {
            console.error(`❌ OPENAI_AGENT_JSON_PARSE_ERROR: Error parsing JSON from assistant:`, error);
            console.error(`❌ OPENAI_AGENT_JSON_PARSE_ERROR_CONTENT: Assistant reply content: ${assistantReply}`);
            throw new Error("Assistant responded with invalid JSON");
          }

          console.log(`✅ OPENAI_AGENT_SUCCESS: Successfully processed agent response on attempt ${attempt}`);
          return parsedReply;
        } else {
          console.log(`❌ OPENAI_AGENT_TIMEOUT: Attempt ${attempt} timed out. Resubmitting the call to OpenAI...`);
          // Loop will retry by creating a new thread and resubmitting the prompt.
        }
      } catch (error) {
        console.error(`❌ OPENAI_AGENT_ATTEMPT_ERROR: Error on attempt ${attempt}:`, error);
        console.error(`❌ OPENAI_AGENT_ATTEMPT_ERROR_STACK:`, error.stack);

        // If this is a critical error that won't be resolved by retrying, throw it
        if (error.message.includes("Assistant responded with invalid JSON") ||
            error.message.includes("No assistant response found")) {
          throw error;
        }

        // For other errors, continue to retry
        console.log(`🔄 OPENAI_AGENT_RETRY: Retrying after error on attempt ${attempt}...`);
      }
    }
  }

  /**
   * Parse a completion from OpenAI
   * @param {string} model - The model to use
   * @param {Array} messages - The messages
   * @param {number} maxCompletionTokens - The maximum number of tokens to generate
   * @returns {Object} - The parsed completion
   */
  async parseCompletion(model, messages, maxCompletionTokens = 16000) {
    let attempt = 1;
    const maxAttempts = 10;

    while (attempt <= maxAttempts) {
      try {
        const completionResults = await this.client.beta.chat.completions.parse({
          model: model,
          messages: messages,
          max_completion_tokens: maxCompletionTokens,
        });

        const contentString = completionResults.choices[0].message.content;
        const parsedJSON = extractJSON(contentString);

        if (parsedJSON !== null) {
          console.log(`Success on attempt ${attempt}`);
          return parsedJSON;
        }

        console.log(`Attempt ${attempt} returned null. Retrying...`);
        attempt++;
      } catch (error) {
        console.error(`Error on attempt ${attempt}:`, error);

        // If we've reached the maximum number of attempts, throw the error
        if (attempt >= maxAttempts) {
          throw error;
        }

        attempt++;
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error(`Failed to parse completion after ${maxAttempts} attempts`);
  }
}

export default OpenAIConnector;
