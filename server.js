// server.js
// Main application entry point

import express from 'express';
import bodyParser from 'body-parser';
import dotenv from 'dotenv';
import { initializeFirebase, getFirestoreDb } from './config/firebase-config.js';
import { Timestamp } from 'firebase-admin/firestore';
import { cors, corsOptions } from './middleware/cors.js';
import { errorHandler } from './middleware/error-handler.js';
import { authenticate } from './middleware/auth.js';
import campaignRoutes from './routes/campaign-routes.js';
import influencerRoutes from './routes/influencer-routes.js';
import analysisRoutes from './routes/analysis-routes.js';
import discoveryRoutes from './routes/discovery-routes.js';
import { generateCampaignBrief } from './services/campaign-service.js';
import { enrichInfluencer } from './services/influencer-service.js';
import { performWebAnalysis, performAestheticAnalysis, performPartnershipAnalysis, performROIAnalysis, getMergedAnalysis } from './services/analysis-service.js';
import { discoverInfluencers } from './services/discovery-service.js';

// Load environment variables
dotenv.config();

// Initialize Firebase using our centralized configuration
initializeFirebase();

// Create Express app
const app = express();

// Apply middleware
app.use(cors(corsOptions));
app.options("*", cors(corsOptions));
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// Health check endpoint (no authentication required)
app.get('/_health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Apply authentication middleware to all other routes
app.use(authenticate);

// Register routes
app.use('/api/campaigns', campaignRoutes);
app.use('/api/influencers', influencerRoutes);
app.use('/api/analysis', analysisRoutes);
app.use('/api/discovery', discoveryRoutes);

// Legacy route for backward compatibility
app.post('/api/analyze', async (req, res) => {
  try {
    const input = req.body;

    // Determine which phase to run based on the input
    if (input.campaign && !input.report_id && !input.selected_account) {
      // Phase 1: Campaign Brief & Audience Analysis
      const result = await generateCampaignBrief(input);
      res.status(200).json(result.campaignData);
    } else if (input.report_id && !input.selected_account) {
      // Phase 2: Broad Influencer Discovery
      const clientId = input.client_id || 'default_client';
      const campaignId = input.report_id;

      // Get the campaign data
      const db = getFirestoreDb();
      const campaignRef = db.collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
      const campaignDoc = await campaignRef.get();

      if (!campaignDoc.exists) {
        throw new Error(`Campaign not found: ${campaignId}`);
      }

      const campaignData = campaignDoc.data();

      // Discover influencers
      const discoveryJSON = await discoverInfluencers(clientId, campaignId, campaignData);
      res.status(200).json(discoveryJSON);
    } else if (input.report_id && input.selected_account) {
      // Phase 3-6: Full Influencer Analysis
      const clientId = input.client_id || 'default_client';
      const campaignId = input.report_id;
      const username = input.selected_account;

      // Enrich the influencer (default to not forcing refresh)
      const forceRefresh = input.force_refresh === true;
      console.log(`Analyzing influencer ${username} with forceRefresh: ${forceRefresh}`);
      const { influencerId, processedData } = await enrichInfluencer(username, 'instagram', forceRefresh);

      // Add the influencer to the campaign
      const db = getFirestoreDb();
      const campaignInfluencerRef = db.collection('clients').doc(clientId)
        .collection('campaigns').doc(campaignId)
        .collection('campaign_influencers').doc(influencerId);

      await campaignInfluencerRef.set({
        influencer_id: influencerId,
        username: username,
        status: 'enriched',
        created_at: Timestamp.now(),
        updated_at: Timestamp.now()
      });

      // Get the campaign data
      const campaignRef = getFirestoreDb().collection('clients').doc(clientId).collection('campaigns').doc(campaignId);
      const campaignDoc = await campaignRef.get();

      if (!campaignDoc.exists) {
        throw new Error(`Campaign not found: ${campaignId}`);
      }

      const campaignData = campaignDoc.data();

      // Perform web analysis
      const influencerName = processedData.profileInfo.fullName || username;
      console.log(`Performing web analysis for ${influencerName} (ID: ${influencerId}) in campaign ${campaignId} for client ${clientId}`);
      // Pass campaign data to web analysis
      const webAnalysisData = await performWebAnalysis(clientId, campaignId, influencerId, influencerName, username);

      // Transform the data for aesthetic analysis
      console.log(`🔄 SERVER_DATA_TRANSFORM_START: Starting data transformation for aesthetic analysis`);
      console.log(`📊 SERVER_PROCESSED_DATA_STRUCTURE: ProcessedData keys:`, Object.keys(processedData || {}));
      console.log(`📊 SERVER_PROFILE_INFO_STRUCTURE: ProfileInfo keys:`, Object.keys(processedData.profileInfo || {}));
      console.log(`📸 SERVER_PROFILE_IMAGE_URL: Profile image URL: ${processedData.profileInfo?.profileImageUrl || 'NULL'}`);
      console.log(`📸 SERVER_ALL_POST_IMAGES: Has allPostImages: ${!!processedData.allPostImages}, Type: ${Array.isArray(processedData.allPostImages) ? 'array' : typeof processedData.allPostImages}, Length: ${Array.isArray(processedData.allPostImages) ? processedData.allPostImages.length : 'N/A'}`);

      const transformedData = {
        instagram: {
          profile_picture_hd: processedData.profileInfo.profileImageUrl || '',
          post_data: []
        }
      };

      console.log(`📊 SERVER_INITIAL_TRANSFORMED_DATA: Initial transformed data structure:`, JSON.stringify(transformedData, null, 2));

      // Use the allPostImages array to populate post_data
      if (processedData.allPostImages && Array.isArray(processedData.allPostImages)) {
        console.log(`🔄 SERVER_POST_PROCESSING_START: Processing ${processedData.allPostImages.length} post images`);

        // Log first few posts for debugging
        processedData.allPostImages.slice(0, 3).forEach((post, index) => {
          console.log(`📊 SERVER_POST_${index + 1}_STRUCTURE: Post ${index + 1} structure:`, JSON.stringify(post, null, 2));
        });

        // Group images by post ID
        const postGroups = {};
        processedData.allPostImages.forEach((post, index) => {
          console.log(`🔍 SERVER_POST_PROCESSING: Processing post ${index + 1}/${processedData.allPostImages.length} with ID: ${post.id}`);

          if (!postGroups[post.id]) {
            postGroups[post.id] = {
              post_id: post.id.toString(),
              caption: post.caption || '',
              engagement: {
                likes: parseInt(post.likes.replace(/,/g, ''), 10) || 0,
                comments: parseInt(post.comments.replace(/,/g, ''), 10) || 0
              },
              media: []
            };
            console.log(`✅ SERVER_POST_GROUP_CREATED: Created new post group for ID: ${post.id}`);
          }

          // Add media item
          const mediaItem = {
            type: post.type || 'image',
            url: post.imageUrl
          };
          postGroups[post.id].media.push(mediaItem);
          console.log(`📸 SERVER_MEDIA_ADDED: Added media item to post ${post.id}: type=${mediaItem.type}, url=${mediaItem.url ? mediaItem.url.substring(0, 50) + '...' : 'NULL'}`);
        });

        // Convert to array
        transformedData.instagram.post_data = Object.values(postGroups);
        console.log(`✅ SERVER_POST_PROCESSING_COMPLETE: Created ${transformedData.instagram.post_data.length} post groups`);

        // Log final post_data structure
        transformedData.instagram.post_data.forEach((post, index) => {
          console.log(`📊 SERVER_FINAL_POST_${index + 1}: Post ${index + 1} - ID: ${post.post_id}, Media count: ${post.media.length}, Caption length: ${post.caption.length}`);
        });
      } else {
        console.log(`❌ SERVER_NO_POST_IMAGES: No allPostImages available for processing`);
      }

      console.log(`📊 SERVER_FINAL_TRANSFORMED_DATA: Final transformed data structure - Profile pic: ${!!transformedData.instagram.profile_picture_hd}, Post count: ${transformedData.instagram.post_data.length}`);
      console.log(`📊 SERVER_FINAL_TRANSFORMED_DATA_DETAIL: Final transformed data:`, JSON.stringify(transformedData, null, 2));

      // Perform aesthetic analysis with the transformed data
      const aestheticAnalysisData = await performAestheticAnalysis(clientId, campaignId, influencerId, influencerName, transformedData, webAnalysisData);

      // Perform partnership analysis
      console.log(`Performing partnership analysis for ${influencerName} (ID: ${influencerId}) in campaign ${campaignId} for client ${clientId}`);
      const partnershipAnalysisData = await performPartnershipAnalysis(clientId, campaignId, influencerId, influencerName, username, campaignData, processedData);

      // Perform ROI analysis
      const roiAnalysisData = await performROIAnalysis(clientId, campaignId, influencerId, influencerName, campaignData, processedData, webAnalysisData, aestheticAnalysisData, partnershipAnalysisData);

      // Generate merged analysis
      const mergedAnalysis = await getMergedAnalysis(clientId, campaignId, influencerId);

      res.status(200).json(mergedAnalysis);
    } else {
      res.status(400).json({ error: 'Invalid input' });
    }
  } catch (error) {
    console.error('Error performing analysis:', error);
    res.status(500).json({ error: 'Failed to perform analysis' });
  }
});

// Error handling middleware
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 8080;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
});

export default app;
